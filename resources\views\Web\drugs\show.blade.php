@extends('layouts.app')

@section('title', 'Drug Details')

@section('content')
    <div class="d-flex flex-column flex-column-fluid drug-show-page">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Drug Details<span class="page-desc text-muted fs-7 fw-semibold pt-1">View comprehensive drug information</span></h1>
                            </div>
                            <div class="d-flex gap-2">
                                <a href="{{ route('drugs.edit', $drug->id) }}" class="btn btn-primary"><i class="fas fa-edit"></i> Edit Drug</a>
                                <a href="{{ route('drugs.index') }}" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to Drugs</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="row g-5 g-xl-10">
                    <!-- Drug Image Section -->
                    <div class="col-xl-4 col-lg-5">
                        <div class="card card-flush">
                            <div class="card-header">
                                <div class="card-title">
                                    <h3 class="fw-bold text-dark">Drug Image</h3>
                                </div>
                            </div>
                            <div class="card-body text-center d-flex flex-column justify-content-center">
                                @if($drug->drug_image)
                                    <div class="image-container mb-4">
                                        <img src="{{ asset('storage/' . $drug->drug_image) }}" alt="{{ $drug->generic_name }}" class="img-fluid rounded shadow-sm" style="max-height: 300px; width: auto; object-fit: contain;">
                                    </div>
                                @else
                                    <div class="d-flex flex-column align-items-center justify-content-center py-10">
                                        <i class="fas fa-pills fa-5x text-muted mb-4"></i>
                                        <h4 class="text-muted">No Image Available</h4>
                                        <p class="text-muted fs-6">Drug image not uploaded</p>
                                    </div>
                                @endif

                                <!-- Drug Basic Info Card -->
                                <div class="bg-light-primary rounded p-4 mt-4">
                                    <div class="d-flex flex-column">
                                        <div class="mb-3">
                                            <span class="badge badge-primary fs-7 mb-2">Serial No.</span>
                                            <div class="fw-bold text-dark">{{ $drug->serial_no ?? 'N/A' }}</div>
                                        </div>
                                        <div class="mb-3">
                                            <span class="badge badge-success fs-7 mb-2">Status</span>
                                            <div>
                                                @if($drug->status == 1)
                                                    <span class="badge badge-light-success">Active</span>
                                                @else
                                                    <span class="badge badge-light-danger">Inactive</span>
                                                @endif
                                            </div>
                                        </div>
                                        <div>
                                            <span class="badge badge-info fs-7 mb-2">Created By</span>
                                            <div class="fw-bold text-dark">{{ $drug->createdBy->full_name ?? 'N/A' }}</div>
                                            <div class="text-muted fs-7">{{ $drug->created_at ? $drug->created_at->format('d M Y, h:i A') : 'N/A' }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Drug Details Section -->
                    <div class="col-xl-8 col-lg-7">
                        <div class="card card-flush h-100">
                            <div class="card-header">
                                <div class="card-title">
                                    <h3 class="fw-bold text-dark">Drug Information</h3>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- Basic Information -->
                                <div class="row mb-8">
                                    <div class="col-12">
                                        <h4 class="text-primary fw-bold mb-4"><i class="fas fa-info-circle me-2"></i>Basic Information</h4>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <label class="form-label fw-semibold fs-6 text-muted">Generic Name</label>
                                        <div class="form-control form-control-solid bg-light">{{ $drug->generic_name ?? 'N/A' }}</div>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <label class="form-label fw-semibold fs-6 text-muted">Brand Name</label>
                                        <div class="form-control form-control-solid bg-light">{{ $drug->brand_name ?? 'N/A' }}</div>
                                    </div>
                                    <div class="col-12 mb-4">
                                        <label class="form-label fw-semibold fs-6 text-muted">IUPAC Name</label>
                                        <div class="form-control form-control-solid bg-light">{{ $drug->iupac_name ?? 'N/A' }}</div>
                                    </div>
                                </div>

                                <!-- Detailed Information -->
                                <div class="row">
                                    <div class="col-12">
                                        <h4 class="text-primary fw-bold mb-4"><i class="fas fa-clipboard-list me-2"></i>Detailed Information</h4>
                                    </div>

                                    <!-- Drug Description -->
                                    <div class="col-12 mb-6">
                                        <label class="form-label fw-semibold fs-6 text-muted">Drug Description</label>
                                        <div class="form-control form-control-solid bg-light ckeditor-content" style="min-height: 120px; overflow-y: auto;">{!! $drug->drug_description ?? '<em class="text-muted">No description available</em>' !!}</div>
                                    </div>

                                    <!-- Drug Indication -->
                                    <div class="col-12 mb-6">
                                        <label class="form-label fw-semibold fs-6 text-muted">Drug Indication</label>
                                        <div class="form-control form-control-solid bg-light ckeditor-content" style="min-height: 120px; overflow-y: auto;">{!! $drug->drug_indication ?? '<em class="text-muted">No indication available</em>' !!}</div>
                                    </div>

                                    <!-- Drug Dosage -->
                                    <div class="col-12 mb-6">
                                        <label class="form-label fw-semibold fs-6 text-muted">Drug Dosage</label>
                                        <div class="form-control form-control-solid bg-light ckeditor-content" style="min-height: 120px; overflow-y: auto;">{!! $drug->drug_dosage ?? '<em class="text-muted">No dosage information available</em>' !!}</div>
                                    </div>

                                    <!-- Route of Administration -->
                                    <div class="col-12 mb-6">
                                        <label class="form-label fw-semibold fs-6 text-muted">Route of Administration</label>
                                        <div class="form-control form-control-solid bg-light ckeditor-content" style="min-height: 120px; overflow-y: auto;">{!! $drug->drug_route_of_administration ?? '<em class="text-muted">No route information available</em>' !!}</div>
                                    </div>

                                    <!-- Drug Frequency -->
                                    <div class="col-12 mb-6">
                                        <label class="form-label fw-semibold fs-6 text-muted">Drug Frequency</label>
                                        <div class="form-control form-control-solid bg-light ckeditor-content" style="min-height: 120px; overflow-y: auto;">{!! $drug->drug_frequency ?? '<em class="text-muted">No frequency information available</em>' !!}</div>
                                    </div>

                                    <!-- Drug Precautions -->
                                    <div class="col-12 mb-6">
                                        <label class="form-label fw-semibold fs-6 text-muted">Drug Precautions</label>
                                        <div class="form-control form-control-solid bg-light ckeditor-content" style="min-height: 120px; overflow-y: auto;">{!! $drug->drug_precautions ?? '<em class="text-muted">No precautions available</em>' !!}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
