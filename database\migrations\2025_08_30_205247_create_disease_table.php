<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('disease', function (Blueprint $table) {
            $table->id();
            $table->longText('disease_name')->nullable();
            $table->longText('disease_cause')->nullable();
            $table->longText('disease_symptoms')->nullable();
            $table->longText('disease_treatment')->nullable();
            $table->longText('disease_prevention')->nullable();
            $table->longText('disease_found_by')->nullable();
            $table->longText('disease_effect')->nullable();
            $table->longText('disease_description')->nullable();
            $table->tinyInteger('status')->default(1);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
            $table->timestamps();
            $table->softDeletes();

            $table->index('disease_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('disease');
    }
};
