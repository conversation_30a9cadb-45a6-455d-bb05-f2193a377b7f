<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('drugs', function (Blueprint $table) {
            $table->id();
            $table->string('serial_no')->nullable();
            $table->longText('generic_name')->nullable();
            $table->longText('brand_name')->nullable();
            $table->longText('iupac_name')->nullable();
            $table->longText('drug_image')->nullable();
            $table->longText('drug_description')->nullable();
            $table->longText('drug_indication')->nullable();
            $table->longText('drug_dosage')->nullable();
            $table->longText('drug_route_of_administration')->nullable();
            $table->longText('drug_frequency')->nullable();
            $table->longText('drug_precautions')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
            $table->tinyInteger('status')->default(1);
            $table->timestamps();
            $table->softDeletes();

            $table->index('generic_name');
            $table->index('brand_name');
            $table->index('iupac_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('drugs');
    }
};
