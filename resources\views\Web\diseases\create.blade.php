@extends('Layouts.app')

@section('title', 'SamRx | Add New Disease')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Add New Disease<span class="page-desc text-muted fs-7 fw-semibold pt-1">Create a new disease entry with comprehensive information</span></h1>
                            </div>
                            <div>
                                <a href="{{ route('diseases.index') }}" class="btn btn-sm btn-primary">Back to Diseases</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <form id="disease-create-form" action="{{ route('diseases.store') }}" method="POST">
                            @csrf
                            <!-- Basic Information Card -->
                            <div class="card card-flush mb-6">
                                <div class="card-header">
                                    <div class="card-title">
                                        <h3 class="fw-bold text-dark"><i class="fas fa-info-circle text-primary me-2"></i>Basic Information</h3>
                                    </div>
                                </div>
                                <div class="card-body pt-6">
                                    <div class="row g-6">
                                        <!-- Disease Name -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="disease_name" class="form-label fw-semibold fs-6 required">Disease Name</label>
                                                <input type="text" name="disease_name" id="disease_name" class="form-control form-control-solid @error('disease_name') is-invalid @enderror" placeholder="Enter disease name" value="{{ old('disease_name') }}" required>
                                                @error('disease_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                        <!-- Disease First Found By -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="disease_found_by" class="form-label fw-semibold fs-6">Disease First Found By</label>
                                                <input type="text" name="disease_found_by" id="disease_found_by" class="form-control form-control-solid @error('disease_found_by') is-invalid @enderror" placeholder="Enter when disease was first found by" value="{{ old('disease_found_by') }}">
                                                @error('disease_found_by')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Enter when or where the disease was first found by</div>
                                            </div>
                                        </div>

                                        <!-- Disease Cause -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="disease_cause" class="form-label fw-semibold fs-6 required">Disease Cause</label>
                                                <textarea name="disease_cause" id="disease_cause" class="form-control form-control-solid ckeditor @error('disease_cause') is-invalid @enderror" rows="4" placeholder="Enter disease cause" required>{{ old('disease_cause') }}</textarea>
                                                @error('disease_cause')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Enter the main causes of the disease</div>
                                            </div>
                                        </div>

                                        <!-- Disease Symptoms -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="disease_symptoms" class="form-label fw-semibold fs-6 required">Disease Symptoms</label>
                                                <textarea name="disease_symptoms" id="disease_symptoms" class="form-control form-control-solid ckeditor @error('disease_symptoms') is-invalid @enderror" rows="4" placeholder="Enter disease symptoms" required>{{ old('disease_symptoms') }}</textarea>
                                                @error('disease_symptoms')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Enter the main symptoms of the disease</div>
                                            </div>
                                        </div>

                                        <!-- Disease Treatment -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="disease_treatment" class="form-label fw-semibold fs-6 required">Disease Treatment</label>
                                                <textarea name="disease_treatment" id="disease_treatment" class="form-control form-control-solid ckeditor @error('disease_treatment') is-invalid @enderror" rows="4" placeholder="Enter disease treatment" required>{{ old('disease_treatment') }}</textarea>
                                                @error('disease_treatment')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Enter the treatment methods for the disease</div>
                                            </div>
                                        </div>

                                        <!-- Disease Prevention -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="disease_prevention" class="form-label fw-semibold fs-6 required">Disease Prevention</label>
                                                <textarea name="disease_prevention" id="disease_prevention" class="form-control form-control-solid ckeditor @error('disease_prevention') is-invalid @enderror" rows="4" placeholder="Enter disease prevention methods" required>{{ old('disease_prevention') }}</textarea>
                                                @error('disease_prevention')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Enter prevention methods for the disease</div>
                                            </div>
                                        </div>

                                        <!-- Disease Effect -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="disease_effect" class="form-label fw-semibold fs-6">Disease Effect</label>
                                                <textarea name="disease_effect" id="disease_effect" class="form-control form-control-solid ckeditor @error('disease_effect') is-invalid @enderror" rows="4" placeholder="Enter disease effects">{{ old('disease_effect') }}</textarea>
                                                @error('disease_effect')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Enter the effects of the disease on the body</div>
                                            </div>
                                        </div>

                                        <!-- Disease Description -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="disease_description" class="form-label fw-semibold fs-6">Disease Description</label>
                                                <textarea name="disease_description" id="disease_description" class="form-control form-control-solid ckeditor @error('disease_description') is-invalid @enderror" placeholder="Enter detailed disease description" rows="4">{{ old('disease_description') }}</textarea>
                                                @error('disease_description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Enter a detailed description of the disease</div>
                                            </div>
                                        </div>

                                        <!-- Status -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="status" class="form-label fw-semibold fs-6 required">Status</label>
                                                <select name="status" id="status" class="form-select form-select-solid @error('status') is-invalid @enderror" required>
                                                    <option value="">Select Status</option>
                                                    <option value="1" {{ old('status') == '1' ? 'selected' : '' }}>Active</option>
                                                    <option value="0" {{ old('status') == '0' ? 'selected' : '' }}>Inactive</option>
                                                </select>
                                                @error('status')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Set the status for this disease</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="card card-flush">
                                <div class="card-body text-center py-8">
                                    <button type="submit" class="btn btn-primary btn-lg me-3" id="submit-btn">
                                        <span class="indicator-label"><i class="fas fa-save me-2"></i>Save Disease</span>
                                        <span class="indicator-progress" style="display: none;">Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                    </button>
                                    <a href="{{ route('diseases.index') }}" class="btn btn-secondary btn-lg"><i class="fas fa-times me-2"></i>Cancel</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Initialize enhanced form with all functionalities
            initializeEnhancedForm({
                formId: 'disease-create-form',
                submitBtnId: 'submit-btn',
                exitSelector: 'a[href="{{ route("diseases.index") }}"]',
                successMessage: 'Disease has been created successfully.',
                redirectUrl: '{{ route("diseases.index") }}',
                hasFileUpload: false
            });
        });
    </script>
@endsection