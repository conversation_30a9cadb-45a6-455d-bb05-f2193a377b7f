<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Disease extends Model
{
    use SoftDeletes;

    protected $table = 'disease';
    protected $fillable = [
        'disease_name',
        'disease_cause',
        'disease_symptoms',
        'disease_treatment',
        'disease_prevention',
        'disease_found_by',
        'disease_effect',
        'disease_description',
        'created_by',
        'status',
    ];

    // ************Relation-With-User************
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    // ************Relation-With-User************
}
