<?php

namespace App\Http\Controllers\Web;

use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\DiseaseSpecialists;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class DiseaseSpecialistsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            try {
                $draw = $request->get('draw');
                $start = $request->get('start');
                $length = $request->get('length');
                $searchValue = $request->get('search')['value'] ?? '';
                $orderColumn = $request->get('order')[0]['column'] ?? 0;
                $orderDirection = $request->get('order')[0]['dir'] ?? 'asc';

                // Define columns for ordering
                $columns = ['id', 'specialist_name', 'specialist_description', 'created_by', 'created_at'];
                $orderBy = $columns[$orderColumn] ?? 'id';

                // Base query
                $query = DiseaseSpecialists::with('createdBy');

                // Apply search filter
                if (!empty($searchValue)) {
                    $query->where(function ($q) use ($searchValue) {
                        $q->where('specialist_name', 'LIKE', "%{$searchValue}%")
                          ->orWhere('specialist_description', 'LIKE', "%{$searchValue}%");
                    });
                }

                // Get total count before pagination
                $totalRecords = DiseaseSpecialists::count();
                $filteredRecords = $query->count();

                // Apply ordering and pagination
                $diseaseSpecialists = $query->orderBy($orderBy, $orderDirection)->skip($start)->take($length)->get();

                // Format data for DataTable
                $data = [];
                foreach ($diseaseSpecialists as $index => $diseaseSpecialist) {
                    $data[] = [
                        'DT_RowIndex' => $start + $index + 1,
                        'id' => $diseaseSpecialist->id,
                        'specialist_name' => $diseaseSpecialist->specialist_name ?? 'N/A',
                        'status' => Helper::getStatusBadge($diseaseSpecialist->status),
                        'created_by' => $diseaseSpecialist->createdBy->full_name ?? 'N/A',
                        'created_at' => Helper::formatDate($diseaseSpecialist->updated_at),
                        'actions' => Helper::getActionButtons($diseaseSpecialist->id, 'disease-specialists')
                    ];
                }

                return response()->json(['draw' => intval($draw), 'recordsTotal' => $totalRecords, 'recordsFiltered' => $filteredRecords, 'data' => $data]);
            } catch (Exception $e) {
                Log::error('Error fetching disease specialists data: ' . $e->getMessage());
                return response()->json(['draw' => intval($request->get('draw')), 'recordsTotal' => 0, 'recordsFiltered' => 0, 'data' => [], 'error' => 'An error occurred while fetching data.'], 500);
            }
        }
        return view('Web.disease-specialists.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('Web.disease-specialists.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'specialist_name' => 'required',
                'specialist_description' => 'required',
                'status' => 'required|in:1,0',
            ]);
            $validatedData['created_by'] = Auth::id();
            DiseaseSpecialists::create($validatedData);
            return response()->json(['success' => true, 'message' => 'Disease Specialist created successfully!']);
        } catch (Exception $e) {
            Log::error('Error creating disease specialist: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while creating the disease specialist.'], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $diseaseSpecialist = DiseaseSpecialists::with('createdBy')->findOrFail($id);
        return view('Web.disease-specialists.show', compact('diseaseSpecialist'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $diseaseSpecialist = DiseaseSpecialists::findOrFail($id);
        return view('Web.disease-specialists.edit', compact('diseaseSpecialist'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $diseaseSpecialist = DiseaseSpecialists::findOrFail($id);

            $validatedData = $request->validate([
                'specialist_name' => 'required',
                'specialist_description' => 'required',
                'status' => 'required|in:1,0',
            ]);

            $diseaseSpecialist->update($validatedData);
            return response()->json(['success' => true, 'message' => 'Disease Specialist updated successfully!']);
        } catch (Exception $e) {
            Log::error('Error updating disease specialist: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while updating the disease specialist.'], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $diseaseSpecialist = DiseaseSpecialists::findOrFail($id);
            $diseaseSpecialist->delete();
            return response()->json(['success' => true, 'message' => 'Disease Specialist deleted successfully!']);
        } catch (Exception $e) {
            Log::error('Error deleting disease specialist: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while deleting the disease specialist.'], 500);
        }
    }
}
