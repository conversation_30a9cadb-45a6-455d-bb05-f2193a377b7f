<?php

use App\Http\Controllers\Frontend\FrontendController;
use App\Http\Controllers\Web\AuthController;
use App\Http\Controllers\Web\AdverseEffectController;
use App\Http\Controllers\Web\ChecmicalStructureController;
use App\Http\Controllers\Web\ClassificationController;
use App\Http\Controllers\Web\DiseaseController;
use App\Http\Controllers\Web\DrugInteractionController;
use App\Http\Controllers\Web\DrugReceptorsController;
use App\Http\Controllers\Web\DrugsController;
use App\Http\Controllers\Web\DrugSchedulesController;
use App\Http\Controllers\Web\DiseaseSpecialistsController;
use App\Http\Controllers\Web\MechanismOfActionController;
use App\Http\Controllers\Web\WebSettingsController;
use Illuminate\Support\Facades\Route;

// Login Routes
Route::get('/admin', [AuthController::class, 'showLoginForm'])->name('showloginform');
Route::post('/login', [AuthController::class, 'login'])->name('login');

// Privacy Policy Routes
Route::get('/privacy-policy', function () {
    return view('Rules.privacy-policy');
})->name('privacy-policy');

// Support Routes
Route::get('/support', function () {
    return view('Rules.support');
})->name('support');

Route::group(['middleware' => ['admin.auth']], function () {
    // Page Under Construction Routes
    Route::get('/page-under-construction', function () {
        return view('Layouts.message');
    })->name('page-under-construction');

    // Dashboard and Logout Routes
    Route::get('/logout', [AuthController::class, 'logout'])->name('logout');
    Route::get('/admin-dashboard', [AuthController::class, 'showDashboard'])->name('admin-dashboard');

    // CRUD Routes
    Route::resource('drugs', DrugsController::class); // Drugs Routes
    Route::resource('diseases', DiseaseController::class); // Diseases Routes
    Route::resource('chemical-structure', ChecmicalStructureController::class); // Chemical Structure Routes
    Route::resource('adverse-effect', AdverseEffectController::class); // Adverse Effects Routes
    Route::resource('classification', ClassificationController::class); // Classification Routes
    Route::resource('drug-Interaction', DrugInteractionController::class); // Drug Interaction Routes
    Route::resource('mechanism-of-action', MechanismOfActionController::class); // Mechanism of Action Routes
    Route::resource('drug-schedules', DrugSchedulesController::class); // Drug Schedules Routes
    Route::resource('drug-receptors', DrugReceptorsController::class); // Drug Receptors Routes
    Route::resource('disease-specialists', DiseaseSpecialistsController::class); // Drug Specialists Routes

    // Web-Settings Routes
    Route::resource('web-settings', WebSettingsController::class);
});

// Frontend Routes
Route::get('/', [FrontendController::class, 'index'])->name('frontend.index');
