<div id="kt_app_footer" class="app-footer">
    <div class="app-container container-fluid d-flex flex-column flex-md-row flex-center flex-md-stack py-3">
        <div class="text-dark order-2 order-md-1">
            <span class="text-muted fw-semibold me-1"><?php echo e(date('Y')); ?>&copy;</span>
            <a href="<?php echo e(route('frontend.index')); ?>" target="_blank" class="text-gray-800 text-hover-primary">SamRx</a>
            <span class="text-muted fw-semibold me-1"> - A full-service healthcare provider</span>
        </div>
        <ul class="menu menu-gray-600 menu-hover-primary fw-semibold order-1">
            <li class="menu-item">
                <a href="privacy-policy" target="_blank" class="menu-link px-2">About</a>
            </li>
            <li class="menu-item">
                <a href="<?php echo e(route('support')); ?>" target="_blank" class="menu-link px-2">Support</a>
            </li>
            <li class="menu-item">
                <a href="<?php echo e(route('web-settings.index')); ?>" class="menu-link px-2">Contact</a>
            </li>
        </ul>
    </div>
</div>




<script src="<?php echo e(asset('plugins/global/plugins.bundle.js')); ?>"></script>
<script src="<?php echo e(asset('js/scripts.bundle.js')); ?>"></script>
<script src="<?php echo e(asset('js/helpers.js')); ?>"></script>


<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>


<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>


<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>


<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>


<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>


<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>


<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>


<script src="https://cdn.ckeditor.com/ckeditor5/40.0.0/classic/ckeditor.js"></script>


<link rel="stylesheet" href="<?php echo e(asset('css/ckeditor-custom.css')); ?>">


<script>
    $(document).ready(function() {
        // Global CSRF Token Setup for all AJAX requests
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // Initialize CKEditor globally for all textareas
        initializeGlobalCKEditor();

        // Initialize CKEditor content display for show pages
        initializeCKEditorContentDisplay();

        // Global AJAX Error Handler
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            if (xhr.status === 419) {
                // CSRF token mismatch
                Swal.fire({
                    icon: 'error',
                    title: 'Session Expired',
                    text: 'Your session has expired. Please refresh the page.',
                    confirmButtonText: 'Refresh Page'
                }).then((result) => {
                    if (result.isConfirmed) {
                        location.reload();
                    }
                });
            } else if (xhr.status === 401) {
                // Unauthorized
                Swal.fire({
                    icon: 'error',
                    title: 'Unauthorized',
                    text: 'You are not authorized to perform this action.',
                    confirmButtonText: 'OK'
                });
            } else if (xhr.status === 500) {
                // Server error
                Swal.fire({
                    icon: 'error',
                    title: 'Server Error',
                    text: 'An internal server error occurred. Please try again later.',
                    confirmButtonText: 'OK'
                });
            }
        });

        // Global loading state for AJAX requests
        $(document).ajaxStart(function() {
            // You can add a global loading indicator here if needed
        }).ajaxStop(function() {
            // Hide global loading indicator here if needed
        });

        // Initialize all tooltips globally
        $('[data-bs-toggle="tooltip"]').tooltip();
        $('[title]').tooltip();

        // Initialize all popovers globally
        $('[data-bs-toggle="popover"]').popover();
    });
</script>


<?php echo $__env->yieldContent('scripts'); ?><?php /**PATH D:\xamp8.2\htdocs\abhishek_work\rx_info\resources\views/Layouts/footer.blade.php ENDPATH**/ ?>