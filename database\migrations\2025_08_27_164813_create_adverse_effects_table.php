<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('adverse_effects', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('drug_id')->nullable();
            $table->foreign('drug_id')->references('id')->on('drugs')->onDelete('cascade');
            $table->longText('adverse_effect')->nullable();
            $table->longText('adverse_effect_description')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
            $table->tinyInteger('status')->default(1);
            $table->tinyInteger('is_common')->default(0);
            $table->tinyInteger('is_rare')->default(0);
            $table->timestamps();
            $table->softDeletes();

            $table->index('drug_id');
            $table->index('is_common');
            $table->index('is_rare');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('adverse_effects');
    }
};
