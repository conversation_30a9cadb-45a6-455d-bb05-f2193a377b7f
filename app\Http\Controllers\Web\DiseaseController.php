<?php

namespace App\Http\Controllers\Web;

use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\Disease;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class DiseaseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            try {
                $draw = $request->get('draw');
                $start = $request->get('start');
                $length = $request->get('length');
                $searchValue = $request->get('search')['value'] ?? '';
                $orderColumn = $request->get('order')[0]['column'] ?? 0;
                $orderDirection = $request->get('order')[0]['dir'] ?? 'asc';

                // Define columns for ordering
                $columns = ['id', 'disease_name', 'created_by', 'status', 'created_at'];
                $orderBy = $columns[$orderColumn] ?? 'id';

                // Base query
                $query = Disease::with('createdBy');

                // Apply search filter
                if (!empty($searchValue)) {
                    $query->where(function ($q) use ($searchValue) {
                        $q->where('disease_name', 'LIKE', "%{$searchValue}%");
                    });
                }

                // Get total count before pagination
                $totalRecords = Disease::count();
                $filteredRecords = $query->count();

                // Apply ordering and pagination
                $diseases = $query->orderBy($orderBy, $orderDirection)->skip($start)->take($length)->get();

                // Format data for DataTable
                $data = [];
                foreach ($diseases as $index => $disease) {
                    $data[] = [
                        'DT_RowIndex' => $start + $index + 1,
                        'id' => $disease->id,
                        'disease_name' => $disease->disease_name ?? 'N/A',
                        'status' => Helper::getStatusBadge($disease->status),
                        'created_by' => $disease->createdBy->full_name ?? 'N/A',
                        'created_at' => Helper::formatDate($disease->updated_at),
                        'actions' => Helper::getActionButtons($disease->id, 'diseases')
                    ];
                }

                return response()->json(['draw' => intval($draw), 'recordsTotal' => $totalRecords, 'recordsFiltered' => $filteredRecords, 'data' => $data]);
            } catch (Exception $e) {
                Log::error('Error fetching diseases data: ' . $e->getMessage());
                return response()->json(['draw' => intval($request->get('draw')), 'recordsTotal' => 0, 'recordsFiltered' => 0, 'data' => [], 'error' => 'An error occurred while fetching data.'], 500);
            }
        }
        return view('Web.diseases.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('Web.diseases.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'disease_name' => 'required',
                'disease_cause' => 'required',
                'disease_symptoms' => 'required',
                'disease_treatment' => 'required',
                'disease_prevention' => 'required',
                'disease_found_by' => 'nullable',
                'disease_effect' => 'nullable',
                'disease_description' => 'nullable',
                'status' => 'required|in:1,0',
            ]);

            $validatedData['created_by'] = Auth::id();
            Disease::create($validatedData);

            return response()->json([
                'success' => true,
                'message' => 'Disease created successfully!'
            ]);
        } catch (Exception $e) {
            Log::error('Error creating disease: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while creating the disease.'
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $disease = Disease::with('createdBy')->findOrFail($id);
            return view('Web.diseases.show', compact('disease'));
        } catch (Exception $e) {
            Log::error('Error showing disease: ' . $e->getMessage());
            return redirect()->route('diseases.index')->with('error', 'Disease not found.');
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        try {
            $disease = Disease::findOrFail($id);
            return view('Web.diseases.edit', compact('disease'));
        } catch (Exception $e) {
            Log::error('Error editing disease: ' . $e->getMessage());
            return redirect()->route('diseases.index')->with('error', 'Disease not found.');
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $disease = Disease::findOrFail($id);

            $validatedData = $request->validate([
                'disease_name' => 'required',
                'disease_cause' => 'required',
                'disease_symptoms' => 'required',
                'disease_treatment' => 'required',
                'disease_prevention' => 'required',
                'disease_found_by' => 'nullable',
                'disease_effect' => 'nullable',
                'disease_description' => 'nullable',
                'status' => 'required|in:1,0',
            ]);

            $disease->update($validatedData);

            return response()->json([
                'success' => true,
                'message' => 'Disease updated successfully!'
            ]);
        } catch (Exception $e) {
            Log::error('Error updating disease: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating the disease.'
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $disease = Disease::findOrFail($id);
            $disease->delete();

            return response()->json([
                'success' => true,
                'message' => 'Disease deleted successfully!'
            ]);
        } catch (Exception $e) {
            Log::error('Error deleting disease: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while deleting the disease.'
            ], 500);
        }
    }
}
