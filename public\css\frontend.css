/* Navbar Styles */
html,
body {
    margin: 0;
    height: 100%;
    width: 100vw;
    overflow-x: hidden;
    padding-top: 40px;
}

/* Change the scroll bar color in dark*/
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}
/* Change the scroll bar color in dark*/

body {
    font-family: "Roboto", sans-serif;
    background: #7fa9d3;
}

h1 {
    margin: 20px 0;
    color: #fff;
}

.layout {
    display: flex;
    flex-direction: column;
    min-height: 100vh; /* full height */
}

.content {
    flex: 1; /* pushes footer to bottom if content is short */
}

.center {
    text-align: center;
}

.logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.logo {
    height: 60px;
}

.nav-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: fixed;          /* 🔥 fix at top */
    top: 0;
    left: 0;
    right: 0;
    height: 80px;
    padding: 0 25px;
    background: #fff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    z-index: 1000;            /* keeps it above everything */
}

/* Center Menu */
.nav-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.nav-tabs {
    display: flex;
    gap: 30px;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-tab a {
    text-decoration: none;
    color: #333;
    font-weight: 600;
    font-size: 16px;
    transition: color 0.3s;
}

.nav-tab a:hover {
    color: #007b83;
}

/* Right Section */
.nav-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.search-box {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.btn-auth {
    padding: 8px 16px;
    background: #439acf;
    color: #fff;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    transition: background 0.3s;
}

.btn-auth:hover {
    background: #0d4f77;
}

@media screen and (max-width: 800px) {
    .nav-container {
        position: fixed;
        display: none;
        overflow-y: auto;
        z-index: -1;
        top: 0;
        right: 0;
        width: 280px;
        height: 100%;
        background: #fff;
        box-shadow: -1px 0 2px rgba(0, 0, 0, 0.2);
    }

    .nav-tabs {
        flex-direction: column;
        align-items: flex-end;
        margin-top: 80px;
        width: 100%;
    }

    .nav-tab:not(:last-child) {
        padding: 20px 25px;
        margin: 0;
        border-right: unset;
        border-bottom: 1px solid #f5f5f5;
    }

    .nav-tab:last-child {
        padding: 15px 25px;
    }

    .menu-btn {
        position: relative;
        display: block;
        margin: 0;
        width: 20px;
        height: 15px;
        cursor: pointer;
        z-index: 2;
        padding: 10px;
        border-radius: 10px;
    }

    .menu-btn .menu {
        display: block;
        width: 100%;
        height: 2px;
        border-radius: 2px;
        background: #111;
    }

    .menu-btn .menu:nth-child(2) {
        margin-top: 4px;
        opacity: 1;
    }

    .menu-btn .menu:nth-child(3) {
        margin-top: 4px;
    }

    #menuToggle:checked+.menu-btn .menu {
        transition: transform 0.2s ease;
    }

    #menuToggle:checked+.menu-btn .menu:nth-child(1) {
        transform: translate3d(0, 6px, 0) rotate(45deg);
    }

    #menuToggle:checked+.menu-btn .menu:nth-child(2) {
        transform: rotate(-45deg) translate3d(-5.71429px, -6px, 0);
        opacity: 0;
    }

    #menuToggle:checked+.menu-btn .menu:nth-child(3) {
        transform: translate3d(0, -6px, 0) rotate(-45deg);
    }

    #menuToggle:checked~.nav-container {
        z-index: 1;
        display: flex;
        animation: menu-slide-left 0.3s ease;
    }

    @keyframes menu-slide-left {
        0% {
            transform: translateX(200px);
        }

        to {
            transform: translateX(0);
        }
    }
}
/* Navbar Styles */

/* Hero Section Styles */
/* Hero Section Styles */
.hero {
    margin-top: 10px;
    padding: 80px 0;
    position: relative;
    overflow: hidden;
    background: #7fa9d3; /* base background color */
}

.hero .container {
    max-width: 1200px;
    margin: auto;
    padding: 0 20px;
}

.hero-content {
    display: flex;
    align-items: flex-start; /* text at top */
    justify-content: space-between;
    flex-wrap: wrap;
    position: relative;
    z-index: 2; /* keep text above overlay */
}

.hero-text {
    flex: 1;
    min-width: 300px;
    margin-top: 15px;
}

.hero-text h1 {
    font-size: 48px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #fff;
}

.hero-text p {
    font-size: 18px;
    color: #f1f1f1;
    margin-bottom: 30px;
    /* set italic font */
    font-style: italic;
}

.hero-text .btn {
    display: inline-block;
    background: #439acf;
    color: #fff;
    padding: 12px 28px;
    border-radius: 6px;
    text-decoration: none;
    transition: 0.3s ease;
}
.hero-text .btn:hover {
    background: #0d4f77;
}

/* Background image overlay on the right */
.hero::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 60%; /* image takes right 60% of hero */
    background: url('/media/frontend/home/<USER>') no-repeat right center/cover;

    /* Fade from left and from top */
    -webkit-mask-image: linear-gradient(to left, rgba(0,0,0,1) 70%, rgba(0,0,0,0) 100%), linear-gradient(to top, rgba(0,0,0,1) 95%, rgba(0,0,0,0) 100%);
    -webkit-mask-composite: destination-in;
    mask-image: linear-gradient(to left, rgba(0,0,0,1) 70%, rgba(0,0,0,0) 100%), linear-gradient(to top, rgba(0,0,0,1) 95%, rgba(0,0,0,0) 100%);
    mask-composite: intersect;
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    opacity: 0.95;
    z-index: 1;
}
/* Hero Section Styles */

/* Footer Styles */
footer {
    display: flex;
    flex-direction: column;
    background: #1a1a1a;
    color: #fff;
    padding: 40px 20px;
    font-family: Arial, sans-serif;
}

.footer-container {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    max-width: 1200px;
    margin: auto;
}

.footer-left, .footer-center, .footer-right {
    flex: 1;
    min-width: 250px;
    margin: 10px;
}

/* Left Side */
.footer-left h3 {
    font-size: 24px;
    margin-bottom: 10px;
}
.footer-left p {
    font-size: 14px;
    line-height: 1.6;
    color: #ccc;
}

/* Center Menu */
.footer-center {
    text-align: center;
    margin-top: 60px;
}
.footer-menu {
    list-style: none;
    padding: 0;
}
.footer-menu li {
    margin: 20px 0;
}
.footer-menu a {
    text-decoration: none;
    color: #fff;
    font-size: 16px;
    transition: color 0.3s;
}
.footer-menu a:hover {
    color: #00bcd4;
}

/* Right Side */
.footer-right {
    text-align: right;
    margin-top: 60px;
}
.socials {
    list-style: none;
    padding: 0;
    margin-bottom: 15px;
}
.socials li {
    display: inline-block;
    margin-left: 10px;
}
.socials a {
    color: #fff;
    font-size: 18px;
    transition: color 0.3s;
}
.socials a:hover {
    color: #00bcd4;
}

.contact-info p {
    margin: 5px 0;
    font-size: 14px;
    color: #ccc;
}

/* Footer Styles */